<script lang="ts" setup>
import { computed, defineEmits, defineProps, onMounted, ref, watch } from 'vue';

import { useDebounceFn } from '@vueuse/core';

defineOptions({
  name: 'RemoteSearchSelect',
});

const props = defineProps({
  /**
   * 用于双向数据绑定的当前选中值
   * 支持多种类型：
   * - 单选时可以是字符串、数字或对象
   * - 多选时为数组
   * 默认值为 null，表示没有选中任何值
   */
  modelValue: {
    type: [String, Number, Object, Array, null],
    default: null,
  },
  /**
   * 输入框的占位符文本
   * 当输入框为空时显示该文本，提示用户输入内容
   * 默认值为 '请选择'
   */
  inputPlaceholder: {
    type: String,
    default: '请选择',
  },
  /**
   * 搜索框的占位符文本
   * 当搜索框为空时显示该文本，提示用户输入内容
   * 默认值为 '请输入关键字搜索'
   */
  placeholder: {
    type: String,
    default: '请输入关键字搜索',
  },
  /**
   * 从远程数据源获取数据的方法
   * 该方法接收一个包含 keyword（搜索关键字）、pageNum（当前页码）和 pageSize（每页数据条数）的对象作为参数
   * 并返回一个包含 records（当前页数据列表）和 total（数据总条数）的结果对象
   * 此属性为必需项
   */
  fetchMethod: {
    type: Function,
    required: true,
  },
  /**
   * 每页显示的数据条数
   * 用于分页查询时控制每页展示的记录数量
   * 默认值为 10
   */
  pageSize: {
    type: Number,
    default: 10,
  },
  /**
   * 选项对象中代表值的键名
   * 在比较和存储选中值时使用该键名来获取对应的值
   * 默认值为 'id'
   */
  valueKey: {
    type: String,
    default: 'id',
  },
  /**
   * 选项对象中代表显示文本的键名
   * 在显示选项和选中值时使用该键名来获取对应的显示文本
   * 默认值为 'name'
   */
  labelKey: {
    type: String,
    default: 'name',
  },
  /**
   * 选项对象中代表显示文本的键名
   * 在显示选项和选中值时使用该键名来获取对应的显示文本
   * 默认值为 'name'
   */
  subLabelKey: {
    type: String,
    default: '',
  },
  /**
   * 防抖时间，单位为毫秒
   * 用于控制搜索请求的频率，避免频繁触发搜索
   * 当用户输入内容时，会等待该时间后才发起搜索请求
   * 默认值为 300
   */
  debounceTime: {
    type: Number,
    default: 300,
  },
  /**
   * 是否在组件挂载时立即发起搜索请求
   * 如果为 true，则组件挂载后会立即调用 fetchMethod 进行数据请求
   * 默认值为 false
   */
  immediateFetch: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否支持多选
   * 如果为 true，则允许用户选择多个选项
   * 如果为 false，则只能选择一个选项
   * 默认值为 false
   */
  multiple: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:modelValue', 'change', 'focus', 'blur']);

interface OptionType {
  [key: string]: any;
}

const inputValue = ref('');
const dataList = ref<OptionType[]>([]);
const total = ref(0);
const currentPage = ref(1);
const isPopoverVisible = ref(false);
const isLoading = ref(true);
const timer = ref<NodeJS.Timeout | null>(null);
const keyword = ref('');
const selectedItems = ref<OptionType[]>([]);

const formattedValue = computed(() => {
  return '';
});

const debouncedSearch = useDebounceFn(
  async (searchKeyword: string, pageNum: number = 1) => {
    try {
      isLoading.value = true;
      keyword.value = searchKeyword;
      currentPage.value = pageNum;

      const result = await props.fetchMethod({
        keyword: searchKeyword,
        pageNum,
        pageSize: props.pageSize,
      });

      dataList.value = result.records || [];
      total.value = result.total || 0;
    } catch (error) {
      console.error('搜索失败:', error);
      dataList.value = [];
      total.value = 0;
    } finally {
      isLoading.value = false;
    }
  },
  props.debounceTime,
);

const searchData = (searchKeyword: string, pageNum: number = 1) => {
  if (timer.value) clearTimeout(timer.value);
  timer.value = setTimeout(() => {
    debouncedSearch(searchKeyword, pageNum);
  }, 50);
};

const selectItem = (item: OptionType) => {
  if (props.multiple) {
    if (
      !selectedItems.value.some(
        (i) => i[props.valueKey] === item[props.valueKey],
      )
    ) {
      selectedItems.value.push(item);
    }
  } else {
    selectedItems.value = [item];
  }

  const values = selectedItems.value.map((i) => i[props.valueKey]);
  emits('update:modelValue', props.multiple ? values : values[0]);
  emits('change', props.multiple ? values : values[0], selectedItems.value);

  isPopoverVisible.value = false;
};

const prevPage = () => {
  if (currentPage.value > 1) {
    searchData(keyword.value, currentPage.value - 1);
  }
};

const nextPage = () => {
  if (currentPage.value * props.pageSize < total.value) {
    searchData(keyword.value, currentPage.value + 1);
  }
};

const handleInput = (value: string) => {
  inputValue.value = value;
  searchData(value, 1);
};

const handleDivClick = () => {
  isPopoverVisible.value = true;
  if (props.immediateFetch) {
    searchData(inputValue.value, 1);
  }
};

const removeSelectedItem = (index: number) => {
  selectedItems.value.splice(index, 1);
  const values = selectedItems.value.map((i) => i[props.valueKey]);
  emits('update:modelValue', props.multiple ? values : values[0]);
  emits('change', props.multiple ? values : values[0], selectedItems.value);
};

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal === null || newVal === undefined) {
      selectedItems.value = [];
      return;
    }
    if (Array.isArray(newVal)) {
      selectedItems.value = newVal.map((val) => {
        if (typeof val === 'object') {
          return val;
        }
        const item = dataList.value.find(
          (item) => item[props.valueKey] === val,
        );
        return item || { [props.valueKey]: val, [props.labelKey]: String(val) };
      });
    } else {
      if (typeof newVal === 'object') {
        selectedItems.value = [newVal];
      } else {
        const item = dataList.value.find(
          (item) => item[props.valueKey] === newVal,
        );
        selectedItems.value = item
          ? [item]
          : [{ [props.valueKey]: newVal, [props.labelKey]: String(newVal) }];
      }
    }
  },
  { immediate: true },
);

onMounted(() => {
  inputValue.value = formattedValue.value;
});
</script>

<template>
  <div class="w-full">
    <!-- 显示选中标签的 div -->
    <div
      @click="handleDivClick"
      class="border-gray-150 h-8 w-full cursor-pointer rounded-md border bg-white py-0.5"
    >
      <div>
        <el-tag
          class="mx-1"
          v-for="(selectedItem, index) in selectedItems"
          :key="index"
          closable
          @close="removeSelectedItem(index)"
        >
          {{ selectedItem[props.labelKey] }}
        </el-tag>
      </div>
      <div
        v-if="selectedItems.length === 0"
        class="ml-3 mt-1 text-sm text-gray-400"
      >
        {{ inputPlaceholder }}
      </div>
    </div>

    <el-popover
      placement="bottom-start"
      v-model:visible="isPopoverVisible"
      trigger="click"
      :width="400"
    >
      <!-- 下拉框中的输入框 -->
      <el-input
        v-model="inputValue"
        :placeholder="placeholder"
        @input="handleInput"
        clearable
        class="w-full"
      >
        <template #suffix>
          <i
            class="el-input__icon"
            :class="
              isPopoverVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
            "
          ></i>
        </template>
      </el-input>
      <el-empty
        v-if="dataList.length === 0 && !isLoading"
        description="暂无数据"
      />
      <ul
        v-else
        class="m-0 max-h-80 list-none overflow-auto p-0"
        v-loading="isLoading"
      >
        <li
          v-for="item in dataList"
          :key="item[props.valueKey]"
          class="cursor-pointer px-3 py-2 transition-all duration-200 hover:bg-gray-50"
          @click="selectItem(item)"
        >
          <slot name="item" :item="item">
            <span>{{ item[props.labelKey] }}</span>
            <span v-if="props.subLabelKey" class="text-gray-400">
              【{{ item[props?.subLabelKey] }}】
            </span>
          </slot>
        </li>
      </ul>
      <div
        v-if="total > 0 && dataList.length > 0"
        class="flex items-center justify-between rounded-b-md border-t border-gray-200 bg-gray-100 px-4 py-2"
      >
        <span class="text-sm text-gray-600">共 {{ total }} 条</span>
        <div class="flex space-x-2">
          <el-button
            size="small"
            :disabled="currentPage === 1"
            @click="prevPage"
            class="rounded-md border border-gray-300 bg-white px-3 py-1 text-xs text-gray-700 transition-all duration-200 hover:bg-gray-50"
          >
            上一页
          </el-button>
          <el-button
            size="small"
            :disabled="currentPage * props.pageSize >= total"
            @click="nextPage"
            class="rounded-md border border-gray-300 bg-white px-3 py-1 text-xs text-gray-700 transition-all duration-200 hover:bg-gray-50"
          >
            下一页
          </el-button>
        </div>
      </div>
      <template #reference>
        <div></div>
      </template>
    </el-popover>
  </div>
</template>

<style scoped>
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.el-select-dropdown {
  box-sizing: border-box;

  /* 其他样式可以根据需要继续调整 */
}

.example-showcase .el-dropdown-link {
  display: flex;
  align-items: center;
  color: var(--el-color-primary);
  cursor: pointer;
}
</style>
