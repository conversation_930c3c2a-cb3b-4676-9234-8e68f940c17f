<script setup lang="ts">
import { h, ref } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getMsg, getRecordListByMessageId } from '#/api/system-settings';

const props = defineProps<{
  messageId: string;
}>();

const loading = ref(false);

const msgRecordList = ref();
/** 文件上传ref*/
const fileRef = ref();

/** 上传列表显示列 */
const defaultTableColumns = {
  fileName: '文件名',
  fileOperate: '操作',
  filePreview: '文件预览',
  fileSize: '文件大小',
};

const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'messageTitle',
      label: '消息标题',
      formItemClass: 'col-span-full items-start',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'messageContent',
      formItemClass: 'col-span-full items-start',
      label: '消息内容',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'messageLevelLabel',
      label: '消息级别',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'sendTypeLabel',
      label: '发送类型',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'sendTime',
      label: '开始发送时间',
    },
    {
      component: 'Upload',
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full items-start',
      label: '附件',
    },
    {
      component: '',
      fieldName: 'receivers',
      formItemClass: 'col-span-full items-start',
      label: '接收人',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

const loadData = async (messageId: string) => {
  try {
    loading.value = true;
    const data = await getMsg(messageId);
    msgRecordList.value = await getRecordListByMessageId({
      messageId,
    });
    formApi.setValues(data);
  } catch {
    ElMessage.error('加载失败');
  } finally {
    loading.value = false;
  }
};
if (props.messageId) {
  loadData(props.messageId);
}

defineExpose({
  formApi,
});
</script>
<template>
  <Form class="text-base" v-loading="loading">
    <template #serialNumber="row">
      <div class="w-full">
        <UploadFiles
          mode="readMode"
          ref="fileRef"
          :show-operat-button="false"
          :show-table="fileRef?.fileList?.length > 0"
          :serial-number="row.value"
          :table-label="defaultTableColumns"
        />
      </div>
    </template>
    <template #receivers>
      <el-table :data="msgRecordList" :border="true" style="width: 100%">
        <el-table-column label="接收人">
          <template #default="scope">
            【{{ scope.row.receiverDeptName }}】{{ scope.row.receiverName }}
          </template>
        </el-table-column>
        <el-table-column prop="isRead" label="是否已读" width="180">
          <template #default="scope">
            <ElTag type="info" effect="light" v-if="scope.row.isRead">
              已读
            </ElTag>
            <ElTag type="primary" effect="light" v-else> 未读 </ElTag>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </Form>
</template>
