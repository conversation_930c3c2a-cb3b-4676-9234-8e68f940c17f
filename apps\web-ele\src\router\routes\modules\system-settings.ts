import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      order: 1,
      title: '系统设置',
    },
    name: 'SystemSettings',
    path: '/system-settings',
    children: [
      {
        component: () => import('#/views/system-settings/role/List.vue'),
        meta: {
          title: '角色管理',
        },
        name: 'role',
        path: '/system-settings/role',
      },
      {
        component: () => import('#/views/system-settings/config/List.vue'),
        meta: {
          title: '系统设置',
        },
        name: 'config',
        path: '/system-settings/config',
      },
      {
        component: () => import('#/views/system-settings/user/List.vue'),
        meta: {
          title: '用户管理',
        },
        name: 'user',
        path: '/system-settings/user',
      },
      {
        component: () =>
          import('#/views/system-settings/users-limited/List.vue'),
        meta: {
          title: '有限授权用户管理',
        },
        name: 'userLimited',
        path: '/system-settings/userLimited',
      },
      {
        component: () =>
          import('#/views/system-settings/users-online/List.vue'),
        meta: {
          title: '在线用户管理',
        },
        name: 'usersOnline',
        path: '/system-settings/usersOnline',
      },
      {
        component: () => import('#/views/system-settings/perms/List.vue'),
        meta: {
          title: '权限管理',
        },
        name: 'perms',
        path: '/system-settings/perms',
      },
      {
        component: () =>
          import('#/views/system-settings/message/send-message/List.vue'),
        meta: {
          title: '发件箱',
        },
        name: 'send-message',
        path: '/system-settings/send-message',
      },
      {
        component: () =>
          import('#/views/system-settings/message/receive-message/List.vue'),
        meta: {
          title: '收件箱',
        },
        name: 'receive-message',
        path: '/system-settings/receive-message',
      },
    ],
  },
];

export default routes;
