import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath, systemPath } from '../path';

export namespace SystemRoleApi {
  export interface recordsType {
    /** 角色id*/
    roleId: string;
    /** 角色名称*/
    roleName: string;
    /** 角色编码*/
    roleCode: string;
    /** 是否启用*/
    isEnable: boolean;
    /** 备注*/
    remark: string;
    /** 创建时间*/
    createTime: string;
  }
  export interface SystemRole {
    [key: string]: any;
    pageNum: number;
    pages: number;
    pageSize: number;
    records: recordsType[];
    total: string;
  }
  /** 角色详情中按钮权限对象*/
  export interface rolePermissionListType {
    /** 是否需要二次校验:是否需要二次校验,true为需要，false为不需要，为空为默认为false */
    isRecheck: boolean;
    /* 按钮权限Id*/
    permissionId: string;
  }
  /** 角色详情中数据条件权限对象*/
  export interface roleConditionListType {
    /** 数据权限Id*/
    conditionId: string;
    /** 数据条件值*/
    conditionValue: string;
  }
  /** 角色详情中字段权限权限对象*/
  export interface roleFieldListType {
    /** 字段权限权限Id*/
    fieldId: string;
    /** 字段权限控制类型*/
    fieldValue: string[];
  }
  /** 返回的角色详情 */
  export interface roleDetailType {
    /** 数据条件权限对象 */
    conditionList: roleConditionListType[];
    /** 字段权限权限对象 */
    fieldList: roleFieldListType[];
    /** 菜单id列表 */
    menusIdList: string[];
    /** 按钮权限对象 */
    permissionList: rolePermissionListType[];
    /** 岗位id列表 */
    positionIds: string[];
    /** 备注*/
    remark: string;
    /** 角色编码 */
    roleCode: string;
    /** 角色id */
    roleId: string;
    /** 角色名称 */
    roleName: string;
  }
}

/** @description 获取角色分页列表 */
export async function getRolePageList(params: Recordable<any>) {
  return requestClient.post<Array<SystemRoleApi.SystemRole>>(
    `${systemPath}/role/getRolePage`,
    { ...params },
  );
}

/** @description 新增角色*/
export async function createRole(params: Recordable<any>) {
  return requestClient.post(`${systemPath}/role/saveRole`, params, {
    headers: {
      'Content-Type-json': 'application/json',
    },
  });
}

/** @description 修改角色*/
export async function updateRole(params: Recordable<any>) {
  return requestClient.post(`${systemPath}/role/modRole`, params, {
    headers: {
      'Content-Type-json': 'application/json',
    },
  });
}

/** @description 根据角色id修改为停用状态*/
export async function disableRole(roleId: string) {
  return requestClient.get(`${systemPath}/role/disableRole/${roleId}`);
}

/** @description 根据角色id修改为启用状态*/
export async function enableRole(roleId: string) {
  return requestClient.get(`${systemPath}/role/enableRole/${roleId}`);
}

/** @description 根据角色id删除角色*/
export async function deleteRole(roleId: string) {
  return requestClient.get(`${systemPath}/role/delRole/${roleId}`);
}

/** @description 查询岗位列表*/
export async function getPositionList(data: any) {
  return requestClient.post(
    `${baseDataPath}/base/position/getPositionList`,
    data,
  );
}

/** @description 获取角色详情 */
export async function getRoleDetail(roleId: string) {
  return requestClient.get<SystemRoleApi.roleDetailType>(
    `${systemPath}/role/getRole/${roleId}`,
  );
}
/** 获取全部角色 */
export async function getAllRoleList() {
  return requestClient.get<
    {
      roleCode: string;
      roleId: string;
      roleName: string;
    }[]
  >(`${systemPath}/role/getAllRoleList`);
}

/** 查询部门列表 */
export async function getDeptList(params?: Recordable<any>) {
  return requestClient.post(`${baseDataPath}/base/dept/getDeptList`, params);
}
