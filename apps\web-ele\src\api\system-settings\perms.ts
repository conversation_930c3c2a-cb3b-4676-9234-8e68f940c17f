import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { systemPath } from '../path';

export namespace SystemPermsApi {
  /** 菜单类型 */
  export interface saveMenu {
    [key: string]: any;
    clientType: string;
    menuIcon?: string;
    menuId: string;
    menuName: string;
    menuOrder?: number;
    menuType: string;
    openType: string;
    parentId?: string;
    targetUrl?: string;
  }
  /** 按钮类型 */
  export interface saveBtn {
    [key: string]: any;
    clientType: string;
    isEnable?: boolean;
    menuId: string;
    permissionCode: string;
    permissionDesc?: string;
    permissionId?: string;
    permissionName: string;
    permissionOrder?: number;
  }

  /** 	字段权限 */
  export interface fieldListType {
    fieldId: string;
    fieldName: string;
    fieldMark: string;
    fieldOrder: number;
    isEnable?: boolean;
    controlType: Array<string>;
  }
  /** 数据条件 */
  export interface conditionListType {
    conditionId: string;
    conditionName: string;
    conditionMark: string;
    conditionOrder: number;
    isEnable?: boolean;
  }
  /** 按钮权限列表 */
  export interface permissionList {
    permissionId: string;
    permissionCode: string;
    permissionName: string;
    permissionOrder: number;
    fieldList: fieldListType[];
    conditionList: conditionListType[];
    isEnable?: boolean;
  }
  /** 返回的菜单树 */
  export interface menuTree {
    [key: string]: any;
    menuId?: string;
    menuName?: string;
    menuCode?: string;
    menuType?: string;
    menuTypeLabel?: string;
    menuIcon?: string;
    menuOrder?: number;
    openType?: string;
    openTypeLabel?: string;
    targetUrl?: string;
    parentId?: string;
    clientType?: string;
    clientTypeLabel?: string;
    children?: menuTree[];
    permissionList?: permissionList[];
    isEnable?: boolean;
  }
}

/** @description 获取所有菜单树*/
export async function getAllMenuTree() {
  return requestClient.get<SystemPermsApi.menuTree>(
    `${systemPath}/perms/getAllMenuTree`,
  );
}

/** @description 新增菜单*/
export async function saveMenu(data: SystemPermsApi.saveMenu) {
  return requestClient.post(`${systemPath}/perms/saveMenu`, data);
}

/** @description 编辑菜单*/
export async function modMenu(data: SystemPermsApi.saveMenu) {
  return requestClient.post(`${systemPath}/perms/modMenu`, data);
}

/** @description 删除菜单*/
export async function delMenu(menuId: string) {
  return requestClient.get(`${systemPath}/perms/delMenu/${menuId}`);
}

/** @description 根据菜单id获取菜单详情*/
export async function getMenu(menuId: string) {
  const res = await requestClient.get(`${systemPath}/perms/getMenu/${menuId}`);
  res.menuOrder = res.menuOrder?.toString();
  return res;
}

/** @description 新增按钮*/
export async function saveBtn(data: SystemPermsApi.saveBtn) {
  return requestClient.post(`${systemPath}/perms/saveBtn`, data);
}

/** @description 编辑按钮*/
export async function modBtn(data: SystemPermsApi.saveBtn) {
  return requestClient.post(`${systemPath}/perms/modBtn`, data);
}

/** @description 删除按钮*/
export async function delBtn(permissionId: string) {
  return requestClient.get(`${systemPath}/perms/delBtn/${permissionId}`);
}

/** @description 根据按钮权限Id获取按钮权限详情*/
export async function getBtn(permissionId: string) {
  return requestClient.get(`${systemPath}/perms/getBtn/${permissionId}`);
}

/** @description 根据按钮Id修改按钮为启用状态*/
export async function enableBtn(permissionId: string) {
  return requestClient.get(`${systemPath}/perms/enableBtn/${permissionId}`);
}

/** @description 根据按钮Id修改按钮为禁用状态*/
export async function disableBtn(permissionId: string) {
  return requestClient.get(`${systemPath}/perms/disableBtn/${permissionId}`);
}

/** @description 根据角色id列表获取菜单树*/
export async function getMenuTreeByRoleIds(params: Recordable<any>) {
  return requestClient.post(`${systemPath}/perms/getMenuTreeByRoleIds`, params);
}

/** @description 根据菜单id列表获取菜单*/
export async function getMenuListByIds(params: Recordable<any>) {
  return requestClient.post(`${systemPath}/perms/getMenuListByIds`, params);
}

/** 根据菜单Id修改菜单为启用状态 */
export async function enableMenu(menuId: string) {
  return requestClient.get(`${systemPath}/perms/enableMenu/${menuId}`);
}

/** 根据菜单Id修改菜单为禁用状态 */
export async function disableMenu(menuId: string) {
  return requestClient.get(`${systemPath}/perms/disableMenu/${menuId}`);
}
