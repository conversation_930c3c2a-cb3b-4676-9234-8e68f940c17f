import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath, messagePath } from '../path';

export namespace SystemMessageApi {
  export interface SystemMessage {
    [key: string]: any;
    records: string;
    total: string;
  }
}

// 获取我发送的站内信分页列表
export async function getSendMsgPage(params: Recordable<any>) {
  return requestClient.post<Array<SystemMessageApi.SystemMessage>>(
    `${baseDataPath}/base/msg/station/record/send/mi/getSendMsgPage`,
    { ...params },
  );
}

// 获取我接收的站内信分页列表
export async function getReceiveMsgPage(params: Recordable<any>) {
  return requestClient.post<Array<SystemMessageApi.SystemMessage>>(
    `${baseDataPath}/base/msg/station/record/mi/getReceiveMsgPage`,
    { ...params },
  );
}

// 获取我接收的站内信分页列表
export async function sendMessage(params: Recordable<any>) {
  return requestClient.post<Array<SystemMessageApi.SystemMessage>>(
    `${messagePath}/msg/station/record/send`,
    { ...params },
  );
}

// 获取站内信详情
export async function getMsg(messageId: string) {
  return requestClient.get(
    `${baseDataPath}/base/msg/station/record/mi/getMsg/${messageId}`,
  );
}

// 获取我接收的站内信详情
export async function getReceiveMsg(messageId: string) {
  return requestClient.get(
    `${baseDataPath}/base/msg/station/record/mi/getReceiveMsg/${messageId}`,
  );
}

// 根据站内信id获取发送记录列表
export async function getRecordListByMessageId(params: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/msg/station/record/getRecordListByMessageId`,
    { ...params },
  );
}
