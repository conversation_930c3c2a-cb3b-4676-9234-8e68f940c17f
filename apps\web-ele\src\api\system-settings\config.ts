import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { systemPath } from '../path';

export namespace SystemConfigApi {
  export interface SystemConfig {
    [key: string]: any;
    records: string;
    total: string;
  }
}

// 获取系统设置分页列表
export async function getConfigPageList(params: Recordable<any>) {
  return requestClient.post<Array<SystemConfigApi.SystemConfig>>(
    `${systemPath}/config/getConfigPage`,
    { ...params },
  );
}

// 新增系统设置
export async function createConfig(
  data: Omit<SystemConfigApi.SystemConfig, 'configId'>,
) {
  return requestClient.post(`${systemPath}/config/saveConfig`, data);
}

// 修改系统设置
export async function updateConfig(
  data: Omit<SystemConfigApi.SystemConfig, 'configId'>,
) {
  return requestClient.post(`${systemPath}/config/modConfig`, data);
}

// 根据系统配置id获取详情
export async function getConfig(configId: string) {
  return requestClient.get(`${systemPath}/config/getConfig/${configId}`);
}

// 删除系统配置
export async function deleteConfig(configId: string) {
  return requestClient.get(`${systemPath}/config/delConfig/${configId}`);
}

// 根据系统配置id禁用配置
export async function disableConfig(configId: string) {
  return requestClient.get(`${systemPath}/config/disableConfig/${configId}`);
}

// 根据系统设置configId修改为启用状态
export async function enableConfig(configId: string) {
  return requestClient.get(`${systemPath}/config/enableConfig/${configId}`);
}

// 系统配置列表模板下载
export async function configTemplate() {
  return requestClient.download(`${systemPath}/config/configTemplate`);
}

// 系统配置列表导出
export async function configExport(params: Recordable<any>) {
  return requestClient.post<Blob>(
    `${systemPath}/config/exportConfig`,
    {
      ...params,
    },
    {
      responseReturn: 'body',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

// 系统配置导入
export async function configImport(data: { file: Blob | File }) {
  return requestClient.upload(`${systemPath}/config/importConfig`, data);
}

// 刷新配置缓存
export async function refreshConfig() {
  return requestClient.get(`${systemPath}/config/refresh`);
}
