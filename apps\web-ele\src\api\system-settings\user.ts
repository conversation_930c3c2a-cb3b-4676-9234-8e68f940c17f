import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { authPath, systemPath } from '../path';

/**
 * @description: 系统用户管理命名空间
 */
export namespace SystemUserApi {
  /** 凭证详细列表 */
  export type onlineList = {
    clientType: string;
    clientTypeLabel: string;
    effectiveTime: string;
    isOnline: boolean;
    loginTime: string;
  };
  /** 用户基本数据 */
  export type recordsType = {
    /** * 头像*/
    avatar: number;
    /** 创建时间*/
    createTime?: string;
    /** 过期时间*/
    expireTime?: string;
    /** 固定菜单*/
    fixedMenuIds?: string[];
    /** 是否启用*/
    isEnable: boolean;
    /** 是否有限授权用户*/
    isLimited?: boolean;
    /** 是否锁定*/
    isLock: boolean;
    /** 手机号*/
    mobilePhone: string;
    /** 昵称*/
    nickname: string;
    /** 在线列表*/
    onlineList?: onlineList[];
    /** 默认打开菜单*/
    openMenuIds?: string[];
    /** 备注*/
    remark?: string;
    /** 角色id列表*/
    roleIdList?: string[];
    /** 用户id*/
    userId: number;
    /** 用户名*/
    username: string;
    /** 用户类型*/
    userType: number;
    /** 用户类型*/
    userTypeLabel: string;
  };
  /** 分页列表 基本*/
  export interface pageList {
    [key: string]: any;
    pageNum: number;
    pages: number;
    pageSize: number;
    records: any[];
    total: string;
  }
  /** 用户分页列表*/
  export interface UserPage extends pageList {
    records: recordsType[];
  }
  /** 当前组织的用户登录记录  records*/
  export interface onlineUserType extends recordsType {
    onlineList: onlineList[];
  }
  /** 当前组织的用户登录记录列表 */
  export interface OnlineUserPage extends pageList {
    records: onlineUserType[];
  }
}

/** 获取用户分页列表*/
export async function getUserPageList(params: Recordable<any>) {
  return requestClient.post<Array<SystemUserApi.UserPage>>(
    `${systemPath}/user/getUserPage`,
    params,
  );
}
/** 获取有限授权用户分页列表 */
export async function getLimitedUserPage(params: Recordable<any>) {
  return requestClient.post<Array<SystemUserApi.UserPage>>(
    `${systemPath}/user/getLimitedUserPage`,
    params,
  );
}
/** 获取当前组织的用户登录记录列表 */
export async function getOnlineUserPage(params: Recordable<any>) {
  return requestClient.post<SystemUserApi.OnlineUserPage>(
    `${systemPath}/user/getOnlineUserPage`,
    params,
  );
}
/** 注销已登录用户*/
export async function kickoutUser(userId: string) {
  return requestClient.get(`${systemPath}/user/kickoutUser/${userId}`);
}
/** 新增用户*/
export async function createUser(params: Recordable<any>) {
  return requestClient.post(`${systemPath}/user/saveUser`, params);
}
/** 新增有限授权用户*/
export async function saveLimitedUser(params: Recordable<any>) {
  return requestClient.post(`${systemPath}/user/saveLimitedUser`, params);
}
/** 修改用户*/
export async function updateUser(params: Recordable<any>) {
  return requestClient.post(`${systemPath}/user/modUser`, params);
}
/** 修改有限授权用户*/
export async function modLimitedUser(params: Recordable<any>) {
  return requestClient.post(`${systemPath}/user/modLimitedUser`, params);
}
/** 用户列表导出*/
export async function exportUser(params: Recordable<any>) {
  return requestClient.post<Blob>(`${systemPath}/user/exportUser`, params, {
    responseReturn: 'body',
    responseType: 'blob', // 设置响应类型为 blob
  });
}
/** 有限授权用户列表导出*/
export async function exportLimitedUser(params: Recordable<any>) {
  return requestClient.post<Blob>(
    `${systemPath}/user/exportLimitedUser`,
    params,
    {
      responseReturn: 'body',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 根据用户id停用用户*/
export async function disableUser(userId: string) {
  return requestClient.get(`${systemPath}/user/disableUser/${userId}`);
}
/** 根据用户id停用有限授权用户*/
export async function disableLimitedUser(userId: string) {
  return requestClient.get(`${systemPath}/user/disableLimitedUser/${userId}`);
}
/** 根据用户id启用用户*/
export async function enableUser(userId: string) {
  return requestClient.get(`${systemPath}/user/enableUser/${userId}`);
}
/** 根据用户id启用有限授权用户*/
export async function enableLimitedUser(userId: string) {
  return requestClient.get(`${systemPath}/user/enableLimitedUser/${userId}`);
}
/** 根据用户id锁定用户*/
export async function lockUser(userId: string) {
  return requestClient.get(`${systemPath}/user/lockUser/${userId}`);
}
/** 根据用户id锁定有限授权用户*/
export async function lockLimitedUser(userId: string) {
  return requestClient.get(`${systemPath}/user/lockLimitedUser/${userId}`);
}
/** 根据用户id解锁用户*/
export async function unlockUser(userId: string) {
  return requestClient.get(`${systemPath}/user/unlockUser/${userId}`);
}
/** 根据用户id解锁有限授权用户*/
export async function unlockLimitedUser(userId: string) {
  return requestClient.get(`${systemPath}/user/unlockLimitedUser/${userId}`);
}
/** 二次校验 */
export async function secondCheck(data: { password: string }) {
  return requestClient.post(`${authPath}/authorize/recheck/user`, data);
}

/**
 * 管理员重置用户密码
 * @description: 注意：管理员重置当前组织内的用户密码，调用该接口前，需要请求二级校验接口校验
 */
export async function resetUserPwd(userId: string) {
  return requestClient.get(`${systemPath}/user/mod/rpw/${userId}`);
}

/** 获取用户类型枚举列表 */
export async function getUserTypeList() {
  return requestClient.get(`${systemPath}/user/getUserTypeList`);
}

/** 获取用户列表 */
export async function getUserList() {
  return requestClient.get(`${systemPath}/user/getUserList`);
}

/** 根据用户id获取用户信息 */
export async function getUserById(userId: string) {
  return requestClient.get<SystemUserApi.recordsType>(
    `${systemPath}/user/getUser/${userId}`,
  );
}
/** 上传头像 */
export async function uploadAvatar(data: {
  file: File; // 上传的文件
}) {
  return requestClient.post(`/file-manage/view/v1/file/upload`, data);
}
